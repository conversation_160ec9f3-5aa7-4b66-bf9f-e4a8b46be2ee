@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    /* Core colors */
    --bg-color: #121212;
    --card-color: #1e1e1e;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --primary-color: #00c6fb;
    --accent: #7a00ff;
    --error: #ff4757;
    --shadow: rgba(0, 0, 0, 0.2);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, #00c6fb, #7a00ff);
    --gradient-secondary: linear-gradient(45deg, #7928CA, #FF0080);
    --gradient-accent: linear-gradient(45deg, #FF9900, #FF007A);
    --gradient-cool: linear-gradient(45deg, #00F5A0, #00D9F5);
    
    /* UI elements */
    --card-radius: 16px;
    --button-radius: 8px;
    --input-radius: 8px;
    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    opacity: 0.6;
}

.gradient-blob {
    position: absolute;
    border-radius: 50%;
    filter: blur(70px);
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
}

.blob-1 {
    background: var(--gradient-primary);
    width: 50vw;
    height: 50vw;
    top: -15%;
    right: -15%;
    animation-duration: 25s;
}

.blob-2 {
    background: var(--gradient-secondary);
    width: 40vw;
    height: 40vw;
    bottom: -15%;
    left: -10%;
    animation-duration: 30s;
    animation-delay: -5s;
}

.blob-3 {
    background: var(--gradient-cool);
    width: 35vw;
    height: 35vw;
    top: 50%;
    left: 60%;
    transform: translateY(-50%);
    animation-duration: 20s;
    animation-delay: -10s;
}

@keyframes float {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.05);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.95);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

/* Main App Container */
.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
    position: relative;
    z-index: 1;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
}

.header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

header h1 {
    font-size: 3.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.25rem;
    letter-spacing: -0.5px;
}

header .tagline {
    font-size: 1.2rem;
    font-weight: 400;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    opacity: 0.8;
}

/* Back to Home Link */
.back-link {
    margin-top: 1rem;
}

.back-link a {
    color: var(--text-secondary);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 1rem;
    padding: 0.5rem 1rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--button-radius);
}

.back-link a:hover {
    color: var(--primary-color);
    transform: translateX(-5px);
    background-color: rgba(255, 255, 255, 0.1);
}

.visualiser-container {
    background-color: var(--card-color);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 1rem;
    padding: 0.5rem 1rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--button-radius);
}

canvas#visualiser {
    width: 100%;
    height: 300px;
    display: block;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.5);
    margin-bottom: 1.5rem;
}

.controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.control-group {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.control-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 50px;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.control-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

.file-upload {
    position: relative;
    overflow: hidden;
}

.file-upload input[type="file"] {
    position: absolute;
    font-size: 100px;
    top: 0;
    right: 0;
    opacity: 0;
    cursor: pointer;
}

.playback-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
}

.playback-controls .control-button {
    padding: 0.75rem;
    border-radius: 50%;
    width: 50px;
    height: 50px;
}

.progress-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.progress-bar {
    height: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    cursor: pointer;
}

.progress {
    height: 100%;
    width: 0;
    background: linear-gradient(to right, var(--primary-color), var(--accent));
    border-radius: 3px;
    transition: width 0.1s ease;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.visualization-options {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 1.5rem;
    background-color: var(--card-color);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.option-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
    flex: 1;
}

.option-group label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.option-group select,
.option-group input[type="range"] {
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 4px;
    color: var(--text-primary);
    padding: 0.5rem;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    outline: none;
}

.option-group select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1em;
    padding-right: 2rem;
}

.option-group input[type="range"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    background-image: linear-gradient(to right, var(--primary-color) 0%, var(--accent) 50%, rgba(255, 255, 255, 0.1) 50%);
    background-size: 200% 100%;
    background-position: right;
    padding: 0;
    margin-top: 0.5rem;
}

.option-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.track-info {
    background-color: var(--card-color);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.info-item {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.info-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

footer {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

footer i {
    color: var(--error);
    margin: 0 0.25rem;
}

.hidden {
    display: none !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    canvas#visualiser {
        height: 200px;
    }
    
    .visualization-options {
        flex-direction: column;
        gap: 1rem;
    }
    
    .option-group {
        min-width: 100%;
    }
    
    .control-group {
        flex-direction: column;
        width: 100%;
    }
    
    .control-button, .file-upload label {
        width: 100%;
    }
}
