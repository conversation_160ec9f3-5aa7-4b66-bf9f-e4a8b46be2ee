# Sound Visualiser

A beautiful web-based sound visualisation application that allows you to visualize audio from your microphone or uploaded audio files with stunning graphics.

## Features

- **Three Visualization Types**:
  - Bars: Classic audio frequency bars
  - Wave: Smooth waveform display
  - Circular: Circular frequency visualization
  
- **Four Color Themes**:
  - Neon Pulse: Vibrant pink and cyan gradients
  - Ocean Waves: Cool blue gradients
  - Fire Ember: Warm red and orange gradients
  - Galaxy: Purple and blue cosmic gradients

- **Audio Sources**:
  - Microphone input
  - Audio file upload support (mp3, wav, ogg, etc.)

- **Playback Controls**:
  - Play/Pause button
  - Progress bar with seeking
  - Time display

- **Adjustable Settings**:
  - Visualization sensitivity control
  
- **Responsive Design**:
  - Works on desktop and mobile devices

## How to Use

1. **Launch the App**: Open `index.html` in a modern web browser
2. **Choose an Audio Source**:
   - Click "Use Microphone" to visualize microphone input
   - Or upload an audio file using the "Upload Audio" button
3. **Control Playback** (for uploaded audio):
   - Use the play/pause button
   - Click anywhere on the progress bar to seek
4. **Customize Visualization**:
   - Choose a visualization type from the dropdown
   - Select a color theme
   - Adjust the sensitivity slider to match your audio input

## Technical Implementation

This app is built using modern web technologies:

- **Web Audio API**: For audio processing and analysis
- **Canvas API**: For rendering real-time visualizations
- **JavaScript**: For UI interactions and audio manipulation
- **CSS**: For styling and responsive design

## Browser Support

This application works best in modern browsers that support the Web Audio API and Canvas, including:
- Google Chrome
- Mozilla Firefox
- Microsoft Edge
- Safari

## License

This project is open-sourced and free to use.
