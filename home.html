<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoundWave - Beautiful Audio Visualisation | Transform Sound into Art</title>
    <meta name="description" content="Experience sound in a new dimension with SoundWave's real-time audio visualizer. Upload music or use your microphone to create stunning visual patterns.">
    <meta name="keywords" content="audio visualizer, sound visualization, music visualizer, real-time audio, web audio API">
    <meta name="author" content="SoundWave">
    <meta property="og:title" content="SoundWave - Transform Your Audio into Visual Art">
    <meta property="og:description" content="Experience sound in a new dimension with our real-time audio visualizer.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://soundwave.app">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="SoundWave - Beautiful Audio Visualisation">
    <meta name="twitter:description" content="Transform your audio into stunning visual art with our real-time visualizer.">

    <!-- Preload critical resources -->
    <link rel="preload" href="home-styles.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style">

    <link rel="stylesheet" href="home-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#6c63ff">
    <meta name="msapplication-TileColor" content="#6c63ff">
</head>
<body>
    <!-- Skip Navigation Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <span class="logo-text">SoundWave</span>
            </div>
            <div class="loading-spinner"></div>
            <p>Loading your audio experience...</p>
        </div>
    </div>

    <!-- Theme Toggle -->
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle dark/light theme">
        <i class="fas fa-moon" aria-hidden="true"></i>
        <i class="fas fa-sun" aria-hidden="true"></i>
    </button>

    <!-- GitHub Pill Component -->
    <div class="github-pill-container">
        <a href="https://github.com/shirishpothi"
           target="_blank"
           rel="noopener noreferrer"
           class="github-pill"
           aria-label="Visit Developer's GitHub Profile">
            <i class="fab fa-github" aria-hidden="true"></i>
            <span>Developer's GitHub</span>
            <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
    </div>

    <!-- Animated background -->
    <div class="animated-background" aria-hidden="true">
        <div class="gradient-blob blob-1"></div>
        <div class="gradient-blob blob-2"></div>
        <div class="gradient-blob blob-3"></div>
    </div>

    <!-- Navigation -->
    <nav role="navigation" aria-label="Main navigation">
        <div class="nav-container">
            <div class="logo">
                <a href="#" aria-label="SoundWave Home">
                    <span class="logo-text">SoundWave</span>
                </a>
            </div>
            <div class="nav-links" role="menubar">
                <a href="#features" role="menuitem">Features</a>
                <a href="#demos" role="menuitem">Gallery</a>
                <a href="#about" role="menuitem">About</a>
                <a href="#faq" role="menuitem">FAQ</a>
                <a href="index.html" class="launch-btn glow-pulse" role="menuitem" aria-describedby="launch-description">
                    Launch App <i class="fas fa-rocket" aria-hidden="true"></i>
                </a>
                <span id="launch-description" class="sr-only">Start using the audio visualizer application</span>
            </div>
            <button class="mobile-menu-icon" aria-label="Open mobile menu" aria-expanded="false" aria-controls="mobile-nav">
                <i class="fas fa-bars" aria-hidden="true"></i>
            </button>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <main id="main-content">
        <section class="hero" aria-labelledby="hero-title">
            <div class="hero-content">
                <h1 id="hero-title" class="hero-title">
                    <span class="hero-title-line">Transform Your</span>
                    <span class="hero-title-line gradient-text">Audio</span>
                    <span class="hero-title-line">into Visual Art</span>
                </h1>
                <p class="hero-description">
                    Experience sound in a whole new dimension with our real-time audio visualizer.
                    Upload your music or use your microphone to create stunning visual patterns that dance to your rhythm.
                </p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">6+</span>
                        <span class="stat-label">Visual Styles</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Color Themes</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <span class="stat-label">Free & Open</span>
                    </div>
                </div>
                <div class="hero-buttons">
                    <a href="index.html" class="primary-button glow-pulse" aria-describedby="get-started-description">
                        Get Started <i class="fas fa-arrow-right" aria-hidden="true"></i>
                    </a>
                    <span id="get-started-description" class="sr-only">Launch the audio visualizer application</span>
                    <a href="#demos" class="secondary-button">
                        View Examples <i class="fas fa-play" aria-hidden="true"></i>
                    </a>
                </div>
            </div>
            <div class="hero-image">
                <div class="demo-visualiser" role="img" aria-label="Live audio visualization demo">
                    <!-- Canvas for animated demo -->
                    <canvas id="heroCanvas" aria-hidden="true"></canvas>
                    <div class="demo-overlay">
                        <div class="demo-controls">
                            <button class="demo-control-btn" aria-label="Play demo audio">
                                <i class="fas fa-play"></i>
                            </button>
                            <div class="demo-info">
                                <span>Live Demo</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    
        <!-- Features Section -->
        <section id="features" class="features" aria-labelledby="features-title">
            <div class="section-header">
                <h2 id="features-title" class="section-title">Powerful <span class="gradient-text">Features</span></h2>
                <p class="section-subtitle">Everything you need to create stunning audio visualizations</p>
            </div>
            <div class="features-grid">
                <article class="feature-card" tabindex="0">
                    <div class="feature-icon" aria-hidden="true">
                        <i class="fas fa-microphone-alt"></i>
                    </div>
                    <h3>Live Microphone Input</h3>
                    <p>Visualize your voice or any ambient sound in real-time with a single click. Perfect for live performances and interactive experiences.</p>
                    <div class="feature-benefits">
                        <span class="benefit-tag">Real-time</span>
                        <span class="benefit-tag">Zero latency</span>
                    </div>
                </article>
                <article class="feature-card" tabindex="0">
                    <div class="feature-icon" aria-hidden="true">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3>Audio File Upload</h3>
                    <p>Upload your favorite music files and watch as they transform into stunning visuals. Supports MP3, WAV, and more formats.</p>
                    <div class="feature-benefits">
                        <span class="benefit-tag">Multiple formats</span>
                        <span class="benefit-tag">High quality</span>
                    </div>
                </article>
                <article class="feature-card" tabindex="0">
                    <div class="feature-icon" aria-hidden="true">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Multiple Visual Styles</h3>
                    <p>Choose from bars, waves, circular patterns, and particle effects to match your aesthetic preference and mood.</p>
                    <div class="feature-benefits">
                        <span class="benefit-tag">6+ styles</span>
                        <span class="benefit-tag">Customizable</span>
                    </div>
                </article>
                <article class="feature-card" tabindex="0">
                    <div class="feature-icon" aria-hidden="true">
                        <i class="fas fa-sliders-h"></i>
                    </div>
                    <h3>Advanced Controls</h3>
                    <p>Fine-tune sensitivity, adjust frequency ranges, and select from beautiful color themes to create your perfect visualization.</p>
                    <div class="feature-benefits">
                        <span class="benefit-tag">Full control</span>
                        <span class="benefit-tag">Professional</span>
                    </div>
                </article>
                <article class="feature-card" tabindex="0">
                    <div class="feature-icon" aria-hidden="true">
                        <i class="fas fa-expand"></i>
                    </div>
                    <h3>Fullscreen Mode</h3>
                    <p>Immerse yourself completely with fullscreen visualizations. Perfect for presentations, parties, and meditation.</p>
                    <div class="feature-benefits">
                        <span class="benefit-tag">Immersive</span>
                        <span class="benefit-tag">Distraction-free</span>
                    </div>
                </article>
                <article class="feature-card" tabindex="0">
                    <div class="feature-icon" aria-hidden="true">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Optimized</h3>
                    <p>Works seamlessly on all devices. Create and enjoy visualizations on your phone, tablet, or desktop computer.</p>
                    <div class="feature-benefits">
                        <span class="benefit-tag">Cross-platform</span>
                        <span class="benefit-tag">Responsive</span>
                    </div>
                </article>
            </div>
        </section>


        <!-- Demos Section -->
        <section id="demos" class="demos" aria-labelledby="demos-title">
            <div class="section-header">
                <h2 id="demos-title" class="section-title">Visualization <span class="gradient-text">Gallery</span></h2>
                <p class="section-subtitle">Explore different visualization styles and themes</p>
            </div>
        <div class="demo-grid">
            <div class="demo-item" data-type="bars" data-theme="gradient1">
                <div class="demo-preview">
                    <canvas class="demo-canvas"></canvas>
                </div>
                <div class="demo-info">
                    <h3>Neon Pulse Bars</h3>
                    <p>Classic frequency bars with vibrant neon colors.</p>
                    <a href="index.html?type=bars&theme=gradient1" class="demo-link">Try it <i class="fas fa-external-link-alt"></i></a>
                </div>
            </div>
            <div class="demo-item" data-type="wave" data-theme="gradient2">
                <div class="demo-preview">
                    <canvas class="demo-canvas"></canvas>
                </div>
                <div class="demo-info">
                    <h3>Ocean Wave</h3>
                    <p>Smooth flowing waveform with oceanic blue hues.</p>
                    <a href="index.html?type=wave&theme=gradient2" class="demo-link">Try it <i class="fas fa-external-link-alt"></i></a>
                </div>
            </div>
            <div class="demo-item" data-type="circular" data-theme="gradient3">
                <div class="demo-preview">
                    <canvas class="demo-canvas"></canvas>
                </div>
                <div class="demo-info">
                    <h3>Fire Orbit</h3>
                    <p>Dynamic circular pattern with fiery red and orange tones.</p>
                    <a href="index.html?type=circular&theme=gradient3" class="demo-link">Try it <i class="fas fa-external-link-alt"></i></a>
                </div>
            </div>
            <div class="demo-item" data-type="circular" data-theme="gradient4">
                <div class="demo-preview">
                    <canvas class="demo-canvas"></canvas>
                </div>
                <div class="demo-info">
                    <h3>Cosmic Spiral</h3>
                    <p>Mesmerizing circular visualization with cosmic purple tones.</p>
                    <a href="index.html?type=circular&theme=gradient4" class="demo-link">Try it <i class="fas fa-external-link-alt"></i></a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- How It Works Section -->
    <section class="how-it-works">
        <h2 class="section-title">How It <span class="gradient-text">Works</span></h2>
        <div class="steps-container">
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h3>Choose Your Input</h3>
                    <p>Select your microphone for live sound or upload an audio file of your choice.</p>
                </div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h3>Select Visualization Style</h3>
                    <p>Pick from bars, waves, or circular patterns to match your preference.</p>
                </div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h3>Customize Your Experience</h3>
                    <p>Choose a color theme and adjust sensitivity for the perfect visualization.</p>
                </div>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h3>Enjoy the Experience</h3>
                    <p>Sit back and watch as your audio transforms into mesmerizing visual art.</p>
                </div>
            </div>
        </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="faq" aria-labelledby="faq-title">
            <div class="section-header">
                <h2 id="faq-title" class="section-title">Frequently Asked <span class="gradient-text">Questions</span></h2>
                <p class="section-subtitle">Everything you need to know about SoundWave</p>
            </div>
            <div class="faq-container">
                <div class="faq-item">
                    <button class="faq-question" aria-expanded="false" aria-controls="faq-answer-1">
                        <span>Is SoundWave completely free to use?</span>
                        <i class="fas fa-chevron-down" aria-hidden="true"></i>
                    </button>
                    <div id="faq-answer-1" class="faq-answer" aria-hidden="true">
                        <p>Yes! SoundWave is completely free to use with no hidden costs, subscriptions, or premium features. All visualization styles and customization options are available to everyone.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question" aria-expanded="false" aria-controls="faq-answer-2">
                        <span>What audio formats are supported?</span>
                        <i class="fas fa-chevron-down" aria-hidden="true"></i>
                    </button>
                    <div id="faq-answer-2" class="faq-answer" aria-hidden="true">
                        <p>SoundWave supports all major audio formats including MP3, WAV, FLAC, AAC, and OGG. You can also use your device's microphone for real-time visualization.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question" aria-expanded="false" aria-controls="faq-answer-3">
                        <span>Does it work on mobile devices?</span>
                        <i class="fas fa-chevron-down" aria-hidden="true"></i>
                    </button>
                    <div id="faq-answer-3" class="faq-answer" aria-hidden="true">
                        <p>Absolutely! SoundWave is fully optimized for mobile devices and tablets. The interface adapts to your screen size for the best experience on any device.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question" aria-expanded="false" aria-controls="faq-answer-4">
                        <span>Can I use this for commercial purposes?</span>
                        <i class="fas fa-chevron-down" aria-hidden="true"></i>
                    </button>
                    <div id="faq-answer-4" class="faq-answer" aria-hidden="true">
                        <p>Yes, you can use SoundWave for commercial purposes including live performances, events, presentations, and content creation. No attribution required.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question" aria-expanded="false" aria-controls="faq-answer-5">
                        <span>Is my audio data stored or shared?</span>
                        <i class="fas fa-chevron-down" aria-hidden="true"></i>
                    </button>
                    <div id="faq-answer-5" class="faq-answer" aria-hidden="true">
                        <p>No, your privacy is our priority. All audio processing happens locally in your browser. No audio data is uploaded, stored, or shared with our servers or third parties.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about" aria-labelledby="about-title">
            <div class="about-container">
                <div class="about-content">
                    <h2 id="about-title" class="section-title">About <span class="gradient-text">SoundWave</span></h2>
                    <p>SoundWave is a powerful audio visualizer built with modern web technologies. Our mission is to transform the way you experience audio by adding a beautiful visual dimension to your music and sounds.</p>
                    <p>Whether you're a music lover, producer, or just curious about audio visualization, SoundWave offers an intuitive interface and stunning visualizations for everyone.</p>
                    <div class="about-cta">
                        <a href="index.html" class="primary-button glow-pulse">Launch App <i class="fas fa-headphones" aria-hidden="true"></i></a>
                        <div class="about-stats">
                            <span class="stat">Privacy focused</span>
                            <span class="stat">Zero data collection</span>
                            <span class="stat">Open source friendly</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <span class="logo-text">SoundWave</span>
            </div>
            <div class="footer-links">
                <a href="#features">Features</a>
                <a href="#demos">Gallery</a>
                <a href="#about">About</a>
                <a href="index.html">Launch App</a>
            </div>
            <div class="footer-social">
                <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                <a href="#" aria-label="GitHub"><i class="fab fa-github"></i></a>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 SoundWave. All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Mobile Navigation -->
    <div id="mobile-nav" class="mobile-nav" aria-hidden="true">
        <div class="mobile-nav-header">
            <span class="mobile-nav-title">Menu</span>
            <button class="mobile-nav-close" aria-label="Close mobile menu">
                <i class="fas fa-times" aria-hidden="true"></i>
            </button>
        </div>
        <nav class="mobile-nav-links" role="navigation" aria-label="Mobile navigation">
            <a href="#features">Features</a>
            <a href="#demos">Gallery</a>
            <a href="#faq">FAQ</a>
            <a href="#about">About</a>
            <a href="index.html" class="mobile-launch-btn">Launch App</a>
        </nav>
    </div>

    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay" aria-hidden="true"></div>
    
    <script src="home-script.js"></script>
</body>
</html>
